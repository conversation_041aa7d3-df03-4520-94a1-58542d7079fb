## Crush Guidance

This file provides guidance to `crush` when working with this repository.

### Backend

- **Manager**: `uv` (pip)
- **Activate**: `source backend/.venv/bin/activate`
- **Install**: `uv pip install -r backend/requirements.txt`
- **Run**: `hypercorn main:app --bind 0.0.0.0:8000 --reload` (in `backend/`)
- **Test**: `python -m pytest backend/test_all_tools.py`
- **Test single**: `python -m pytest backend/test_all_tools.py -k test_function_name`

### Frontend

- **Manager**: `npm`
- **Install**: `npm install` (in `frontend/`)
- **Run**: `npm start` (in `frontend/`)
- **Test**: `npm test` (in `frontend/`)
- **Test single**: `npm test -- frontend/src/App.test.js`

### Code Style

- **Python**: Use `google-genai` for AI. Follow structured logging.
- **Frontend**: Use React 19+ with functional components and hooks. Use FontAwesome icons.
- **Git Commits**: Follow conventional commit guidelines.
- **CI/CD**: Changes are deployed via Google Cloud Build and Cloud Run.
- **Dependencies**: Keep `requirements.txt` and `package.json` up to date.
- **Environment**: Use `.env.example` as a template.
